# Server Configuration
PORT=3000
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/whatsapp-ai-assistant
MONGODB_TEST_URI=mongodb://localhost:27017/whatsapp-ai-assistant-test

# WhatsApp Business API Configuration
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id

# AI API Configuration (Choose one or both)
# Claude API
ANTHROPIC_API_KEY=your_anthropic_api_key

# Gemini API
GOOGLE_AI_API_KEY=your_google_ai_api_key

# Domain Registrar APIs (Choose one)
# GoDaddy API
GODADDY_API_KEY=your_godaddy_api_key
GODADDY_API_SECRET=your_godaddy_api_secret
GODADDY_ENVIRONMENT=production

# Namecheap API
NAMECHEAP_API_USER=your_namecheap_username
NAMECHEAP_API_KEY=your_namecheap_api_key
NAMECHEAP_CLIENT_IP=your_client_ip

# Hosting Provider APIs (Choose one or more)
# DigitalOcean API
DIGITALOCEAN_ACCESS_TOKEN=your_digitalocean_token

# Vercel API
VERCEL_ACCESS_TOKEN=your_vercel_token

# Hostinger API
HOSTINGER_API_KEY=your_hostinger_api_key

# Security Configuration
JWT_SECRET=your_jwt_secret_key_here
ENCRYPTION_KEY=your_32_character_encryption_key

# File Storage Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=********

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Session Configuration
SESSION_TIMEOUT_MINUTES=30
MAX_CONCURRENT_SESSIONS=5

# Deployment Configuration
DEFAULT_HOSTING_PROVIDER=vercel
DEPLOYMENT_TIMEOUT_MINUTES=10

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret_for_signature_verification
