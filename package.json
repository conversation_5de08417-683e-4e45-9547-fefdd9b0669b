{"name": "whatsapp-ai-coding-assistant", "version": "1.0.0", "description": "AI-powered coding assistant that operates through WhatsApp Business API with domain and hosting integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "docker:build": "docker build -t whatsapp-ai-assistant .", "docker:run": "docker run -p 3000:3000 --env-file .env whatsapp-ai-assistant"}, "keywords": ["whatsapp", "ai", "coding-assistant", "claude", "gemini", "domain", "hosting", "deployment"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "dotenv": "^16.3.1", "axios": "^1.6.2", "crypto": "^1.0.1", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "uuid": "^9.0.1", "archiver": "^6.0.1", "form-data": "^4.0.0", "node-cron": "^3.0.3", "winston": "^3.11.0", "express-winston": "^4.2.0", "@anthropic-ai/sdk": "^0.9.1", "@google/generative-ai": "^0.2.1", "whatsapp-web.js": "^1.23.0", "qrcode-terminal": "^0.12.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/whatsapp-ai-coding-assistant.git"}, "bugs": {"url": "https://github.com/yourusername/whatsapp-ai-coding-assistant/issues"}, "homepage": "https://github.com/yourusername/whatsapp-ai-coding-assistant#readme"}