const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const winston = require('winston');
const expressWinston = require('express-winston');
require('dotenv').config();

const DatabaseManager = require('./services/DatabaseManager');
const WhatsAppHandler = require('./handlers/WhatsAppHandler');
const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
const webhookRoutes = require('./routes/webhook');
const healthRoutes = require('./routes/health');

class Server {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3000;
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use('/webhook', limiter);

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging middleware
    this.app.use(expressWinston.logger({
      winstonInstance: logger,
      meta: true,
      msg: 'HTTP {{req.method}} {{req.url}}',
      expressFormat: true,
      colorize: false,
      ignoreRoute: function (req, res) {
        return req.url === '/health';
      }
    }));
  }

  setupRoutes() {
    // Health check route
    this.app.use('/health', healthRoutes);
    
    // WhatsApp webhook routes
    this.app.use('/webhook', webhookRoutes);

    // Root route
    this.app.get('/', (req, res) => {
      res.json({
        message: 'WhatsApp AI Coding Assistant API',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString()
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Route not found',
        message: `Cannot ${req.method} ${req.originalUrl}`
      });
    });
  }

  setupErrorHandling() {
    // Error logging middleware
    this.app.use(expressWinston.errorLogger({
      winstonInstance: logger
    }));

    // Global error handler
    this.app.use(errorHandler);
  }

  async start() {
    try {
      // Initialize database connection
      await DatabaseManager.connect();
      logger.info('Database connected successfully');

      // Initialize WhatsApp handler
      const whatsappHandler = new WhatsAppHandler();
      await whatsappHandler.initialize();
      logger.info('WhatsApp handler initialized');

      // Start server
      this.server = this.app.listen(this.port, () => {
        logger.info(`Server running on port ${this.port} in ${process.env.NODE_ENV} mode`);
        console.log(`🚀 WhatsApp AI Coding Assistant API running on http://localhost:${this.port}`);
      });

      // Graceful shutdown handling
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  setupGracefulShutdown() {
    const gracefulShutdown = async (signal) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);
      
      if (this.server) {
        this.server.close(async () => {
          logger.info('HTTP server closed');
          
          try {
            await DatabaseManager.disconnect();
            logger.info('Database disconnected');
            process.exit(0);
          } catch (error) {
            logger.error('Error during shutdown:', error);
            process.exit(1);
          }
        });
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new Server();
  server.start();
}

module.exports = Server;
