const axios = require('axios');
const { Project, Deployment } = require('../models');
const logger = require('../utils/logger');

class DomainHostingHandler {
  constructor() {
    this.logger = logger.child({ module: 'DomainHostingHandler' });
    
    // Domain registrar configurations
    this.godaddyConfig = {
      apiKey: process.env.GODADDY_API_KEY,
      apiSecret: process.env.GODADDY_API_SECRET,
      environment: process.env.GODADDY_ENVIRONMENT || 'production',
      baseUrl: process.env.GODADDY_ENVIRONMENT === 'production' 
        ? 'https://api.godaddy.com' 
        : 'https://api.ote-godaddy.com'
    };
    
    this.namecheapConfig = {
      apiUser: process.env.NAMECHEAP_API_USER,
      apiKey: process.env.NAMECHEAP_API_KEY,
      clientIp: process.env.NAMECHEAP_CLIENT_IP,
      baseUrl: 'https://api.namecheap.com/xml.response'
    };
    
    // Hosting provider configurations
    this.vercelConfig = {
      token: process.env.VERCEL_ACCESS_TOKEN,
      baseUrl: 'https://api.vercel.com'
    };
    
    this.digitalOceanConfig = {
      token: process.env.DIGITALOCEAN_ACCESS_TOKEN,
      baseUrl: 'https://api.digitalocean.com/v2'
    };
  }

  async handleDomainSelection(session, messageContent) {
    const phoneNumber = session.whatsappNumber;
    const domainName = this.extractDomainName(messageContent);
    
    if (!domainName) {
      await this.sendMessage(phoneNumber, 
        "Please provide a valid domain name (e.g., 'mysite.com' or 'awesome-business.org')"
      );
      return;
    }
    
    try {
      // Check domain availability
      const availability = await this.checkDomainAvailability(domainName);
      
      if (availability.available) {
        await this.showDomainPurchaseOptions(session, domainName, availability.price);
      } else {
        await this.suggestAlternativeDomains(session, domainName);
      }
      
    } catch (error) {
      this.logger.error('Error checking domain availability:', error);
      await this.sendMessage(phoneNumber, 
        "I had trouble checking that domain. Please try another domain name."
      );
    }
  }

  extractDomainName(messageContent) {
    // Extract domain name from user message
    const domainRegex = /([a-zA-Z0-9-]+\.(?:com|org|net|io|co|app|dev|tech|online|site|website|biz|info))/i;
    const match = messageContent.match(domainRegex);
    
    if (match) {
      return match[1].toLowerCase();
    }
    
    // Try to construct domain from text
    const words = messageContent.toLowerCase()
      .replace(/[^a-zA-Z0-9\s-]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 0);
    
    if (words.length > 0) {
      const domainBase = words.join('-').substring(0, 50);
      return `${domainBase}.com`;
    }
    
    return null;
  }

  async checkDomainAvailability(domainName) {
    try {
      // Try GoDaddy first, then Namecheap
      if (this.godaddyConfig.apiKey) {
        return await this.checkDomainGoDaddy(domainName);
      } else if (this.namecheapConfig.apiKey) {
        return await this.checkDomainNamecheap(domainName);
      } else {
        // Mock response for development
        return {
          available: Math.random() > 0.5,
          price: 12.99,
          registrar: 'mock'
        };
      }
    } catch (error) {
      this.logger.error('Error checking domain availability:', error);
      throw error;
    }
  }

  async checkDomainGoDaddy(domainName) {
    try {
      const response = await axios.get(
        `${this.godaddyConfig.baseUrl}/v1/domains/available?domain=${domainName}`,
        {
          headers: {
            'Authorization': `sso-key ${this.godaddyConfig.apiKey}:${this.godaddyConfig.apiSecret}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      return {
        available: response.data.available,
        price: response.data.price || 12.99,
        registrar: 'godaddy'
      };
    } catch (error) {
      this.logger.error('GoDaddy API error:', error.response?.data || error.message);
      throw error;
    }
  }

  async checkDomainNamecheap(domainName) {
    try {
      const params = new URLSearchParams({
        ApiUser: this.namecheapConfig.apiUser,
        ApiKey: this.namecheapConfig.apiKey,
        UserName: this.namecheapConfig.apiUser,
        Command: 'namecheap.domains.check',
        ClientIp: this.namecheapConfig.clientIp,
        DomainList: domainName
      });
      
      const response = await axios.get(`${this.namecheapConfig.baseUrl}?${params}`);
      
      // Parse XML response (simplified)
      const available = response.data.includes('Available="true"');
      
      return {
        available,
        price: 10.99, // Default price
        registrar: 'namecheap'
      };
    } catch (error) {
      this.logger.error('Namecheap API error:', error.response?.data || error.message);
      throw error;
    }
  }

  async showDomainPurchaseOptions(session, domainName, price) {
    const phoneNumber = session.whatsappNumber;
    
    await session.updateState('selecting_domain', {
      domain: {
        searchQuery: domainName,
        selectedDomain: domainName,
        price: price
      }
    });
    
    const message = `✅ **${domainName}** is available!

💰 **Price:** $${price}/year

Would you like to:
1️⃣ Purchase this domain ($${price})
2️⃣ Try a different domain
3️⃣ Skip domain for now and deploy

Reply with your choice!`;

    await this.sendMessage(phoneNumber, message);
  }

  async suggestAlternativeDomains(session, originalDomain) {
    const phoneNumber = session.whatsappNumber;
    const baseName = originalDomain.split('.')[0];
    
    // Generate alternative suggestions
    const alternatives = [
      `${baseName}.org`,
      `${baseName}.net`,
      `${baseName}.io`,
      `get${baseName}.com`,
      `${baseName}app.com`
    ];
    
    const message = `❌ **${originalDomain}** is not available.

Here are some alternatives:
${alternatives.map((alt, i) => `${i + 1}️⃣ ${alt}`).join('\n')}

Reply with:
- The number of your choice
- A different domain name
- "Skip domain" to proceed without a domain`;

    await this.sendMessage(phoneNumber, message);
  }

  async handleHostingSelection(session, messageContent) {
    const phoneNumber = session.whatsappNumber;
    const lowerMessage = messageContent.toLowerCase();
    
    let provider = null;
    
    if (lowerMessage.includes('1') || lowerMessage.includes('vercel')) {
      provider = 'vercel';
    } else if (lowerMessage.includes('2') || lowerMessage.includes('netlify')) {
      provider = 'netlify';
    } else if (lowerMessage.includes('3') || lowerMessage.includes('digitalocean')) {
      provider = 'digitalocean';
    }
    
    if (!provider) {
      await this.sendMessage(phoneNumber, 
        "Please choose a hosting provider:\n1️⃣ Vercel\n2️⃣ Netlify\n3️⃣ DigitalOcean"
      );
      return;
    }
    
    await this.startDeployment(session, provider);
  }

  async startDeployment(session, provider) {
    const phoneNumber = session.whatsappNumber;
    
    await session.updateState('deploying', {
      hosting: {
        provider: provider,
        status: 'pending'
      }
    });
    
    await this.sendMessage(phoneNumber, 
      `🚀 Starting deployment to ${provider}... This may take a few minutes.`
    );
    
    try {
      // Get the project
      const project = await Project.findOne({ sessionId: session.sessionId });
      if (!project) {
        throw new Error('Project not found');
      }
      
      // Create deployment record
      const deployment = await Deployment.createDeployment(
        project._id,
        session.userId,
        provider
      );
      
      // Start deployment process
      const deploymentResult = await this.deployToProvider(project, deployment, provider);
      
      if (deploymentResult.success) {
        await this.handleSuccessfulDeployment(session, project, deployment, deploymentResult);
      } else {
        await this.handleFailedDeployment(session, deployment, deploymentResult.error);
      }
      
    } catch (error) {
      this.logger.error('Deployment error:', error);
      await this.sendMessage(phoneNumber, 
        "❌ Deployment failed. Please try again or contact support."
      );
      await session.updateState('code_ready');
    }
  }

  async deployToProvider(project, deployment, provider) {
    try {
      switch (provider) {
        case 'vercel':
          return await this.deployToVercel(project, deployment);
        case 'netlify':
          return await this.deployToNetlify(project, deployment);
        case 'digitalocean':
          return await this.deployToDigitalOcean(project, deployment);
        default:
          throw new Error(`Unsupported provider: ${provider}`);
      }
    } catch (error) {
      this.logger.error(`Deployment to ${provider} failed:`, error);
      return { success: false, error: error.message };
    }
  }

  async deployToVercel(project, deployment) {
    try {
      if (!this.vercelConfig.token) {
        return { success: false, error: 'Vercel token not configured' };
      }
      
      // Create deployment payload
      const files = {};
      project.codebase.files.forEach(file => {
        files[file.path] = { file: Buffer.from(file.content).toString('base64') };
      });
      
      const deploymentPayload = {
        name: project.name.toLowerCase().replace(/\s+/g, '-'),
        files: files,
        projectSettings: {
          framework: project.codebase.framework === 'React' ? 'nextjs' : null
        }
      };
      
      const response = await axios.post(
        `${this.vercelConfig.baseUrl}/v13/deployments`,
        deploymentPayload,
        {
          headers: {
            'Authorization': `Bearer ${this.vercelConfig.token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      await deployment.updateStatus('deployed', {
        'deployment.url': response.data.url,
        'deployment.buildId': response.data.id
      });
      
      return {
        success: true,
        url: `https://${response.data.url}`,
        deploymentId: response.data.id
      };
      
    } catch (error) {
      this.logger.error('Vercel deployment error:', error.response?.data || error.message);
      return { success: false, error: error.message };
    }
  }

  async deployToNetlify(project, deployment) {
    // Mock implementation for Netlify
    await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate deployment time
    
    const mockUrl = `https://${project.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.netlify.app`;
    
    await deployment.updateStatus('deployed', {
      'deployment.url': mockUrl,
      'deployment.buildId': `netlify-${Date.now()}`
    });
    
    return {
      success: true,
      url: mockUrl,
      deploymentId: `netlify-${Date.now()}`
    };
  }

  async deployToDigitalOcean(project, deployment) {
    // Mock implementation for DigitalOcean
    await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate deployment time
    
    const mockUrl = `https://${project.name.toLowerCase().replace(/\s+/g, '-')}.do.app`;
    
    await deployment.updateStatus('deployed', {
      'deployment.url': mockUrl,
      'deployment.buildId': `do-${Date.now()}`
    });
    
    return {
      success: true,
      url: mockUrl,
      deploymentId: `do-${Date.now()}`
    };
  }

  async handleSuccessfulDeployment(session, project, deployment, deploymentResult) {
    const phoneNumber = session.whatsappNumber;
    
    // Update project status
    await project.setHosting({
      provider: deployment.provider,
      deploymentUrl: deploymentResult.url,
      deploymentId: deploymentResult.deploymentId,
      status: 'deployed'
    });
    
    await session.updateState('deployed');
    
    const message = `🎉 **Deployment Successful!**

Your website is now live at:
🌐 ${deploymentResult.url}

**What's next?**
- Share your website with others
- Make updates to your code
- Set up a custom domain
- Monitor your site's performance

Congratulations on launching your website! 🚀`;

    await this.sendMessage(phoneNumber, message);
  }

  async handleFailedDeployment(session, deployment, error) {
    const phoneNumber = session.whatsappNumber;
    
    await deployment.setError('DEPLOYMENT_FAILED', error);
    await session.updateState('code_ready');
    
    const message = `❌ **Deployment Failed**

Error: ${error}

Don't worry! You can:
1️⃣ Try a different hosting provider
2️⃣ Retry the same provider
3️⃣ Download your code and deploy manually

What would you like to do?`;

    await this.sendMessage(phoneNumber, message);
  }

  async sendMessage(phoneNumber, message) {
    // This will be handled by WhatsAppHandler
    const WhatsAppHandler = require('./WhatsAppHandler');
    const handler = new WhatsAppHandler();
    return handler.sendMessage(phoneNumber, message);
  }
}

module.exports = DomainHostingHandler;
