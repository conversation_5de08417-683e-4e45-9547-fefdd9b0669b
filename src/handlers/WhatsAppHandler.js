const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');
const UserManager = require('../services/UserManager');
const AICodeAgent = require('./AICodeAgent');
const DomainHostingHandler = require('./DomainHostingHandler');

class WhatsAppHandler {
  constructor() {
    this.accessToken = process.env.WHATSAPP_ACCESS_TOKEN;
    this.phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID;
    this.apiUrl = `https://graph.facebook.com/v18.0/${this.phoneNumberId}`;
    this.userManager = new UserManager();
    this.aiAgent = new AICodeAgent();
    this.domainHostingHandler = new DomainHostingHandler();
    this.logger = logger.child({ module: 'WhatsAppHandler' });
  }

  async initialize() {
    try {
      // Verify WhatsApp API credentials
      await this.verifyCredentials();
      this.logger.info('WhatsApp handler initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize WhatsApp handler:', error);
      throw error;
    }
  }

  async verifyCredentials() {
    try {
      const response = await axios.get(`${this.apiUrl}`, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });
      
      this.logger.info('WhatsApp API credentials verified', {
        phoneNumberId: this.phoneNumberId,
        displayName: response.data.display_phone_number
      });
      
      return response.data;
    } catch (error) {
      this.logger.error('WhatsApp API credential verification failed:', error.response?.data || error.message);
      throw new Error('Invalid WhatsApp API credentials');
    }
  }

  async handleWebhook(webhookData) {
    try {
      if (!webhookData.entry || !Array.isArray(webhookData.entry)) {
        this.logger.warn('Invalid webhook data structure');
        return;
      }

      for (const entry of webhookData.entry) {
        if (!entry.changes) continue;

        for (const change of entry.changes) {
          if (change.field === 'messages') {
            await this.handleMessages(change.value);
          } else if (change.field === 'message_status') {
            await this.handleMessageStatus(change.value);
          }
        }
      }
    } catch (error) {
      this.logger.error('Error handling webhook:', error);
    }
  }

  async handleMessages(messageData) {
    try {
      if (!messageData.messages || !Array.isArray(messageData.messages)) {
        return;
      }

      for (const message of messageData.messages) {
        await this.processMessage(message);
      }
    } catch (error) {
      this.logger.error('Error handling messages:', error);
    }
  }

  async processMessage(message) {
    try {
      const phoneNumber = message.from;
      const messageId = message.id;
      const timestamp = message.timestamp;

      this.logger.info('Processing message', {
        from: phoneNumber,
        messageId,
        type: message.type
      });

      // Get or create user
      let user = await this.userManager.findOrCreateUser(phoneNumber);
      
      // Get or create session
      let session = await this.userManager.getActiveSession(phoneNumber);
      if (!session) {
        session = await this.userManager.createSession(user._id, phoneNumber);
      }

      // Add message to session
      const messageContent = this.extractMessageContent(message);
      await session.addMessage('incoming', messageContent, message.type);

      // Process the message based on current session state
      await this.routeMessage(session, messageContent, message);

    } catch (error) {
      this.logger.error('Error processing message:', error);
      // Send error message to user
      await this.sendMessage(message.from, 
        "Sorry, I encountered an error processing your message. Please try again later."
      );
    }
  }

  extractMessageContent(message) {
    switch (message.type) {
      case 'text':
        return message.text.body;
      case 'image':
        return `[Image: ${message.image.caption || 'No caption'}]`;
      case 'document':
        return `[Document: ${message.document.filename || 'Unknown file'}]`;
      case 'audio':
        return '[Audio message]';
      default:
        return '[Unsupported message type]';
    }
  }

  async routeMessage(session, messageContent, originalMessage) {
    const state = session.state;
    const phoneNumber = session.whatsappNumber;

    this.logger.info('Routing message', {
      phoneNumber,
      state,
      messageContent: messageContent.substring(0, 100)
    });

    try {
      switch (state) {
        case 'idle':
          await this.handleIdleState(session, messageContent);
          break;
        case 'gathering_requirements':
          await this.handleRequirementsGathering(session, messageContent);
          break;
        case 'confirming_requirements':
          await this.handleRequirementsConfirmation(session, messageContent);
          break;
        case 'generating_code':
          await this.sendMessage(phoneNumber, "I'm still working on generating your code. Please wait a moment...");
          break;
        case 'code_ready':
          await this.handleCodeReady(session, messageContent);
          break;
        case 'selecting_domain':
          await this.handleDomainSelection(session, messageContent);
          break;
        case 'selecting_hosting':
          await this.handleHostingSelection(session, messageContent);
          break;
        case 'deploying':
          await this.sendMessage(phoneNumber, "Your website is being deployed. I'll notify you when it's ready!");
          break;
        default:
          await this.handleUnknownState(session, messageContent);
      }
    } catch (error) {
      this.logger.error('Error routing message:', error);
      await this.sendMessage(phoneNumber, 
        "I encountered an error processing your request. Let me reset our conversation. Type 'help' to start over."
      );
      await session.updateState('idle');
    }
  }

  async handleIdleState(session, messageContent) {
    const phoneNumber = session.whatsappNumber;
    const lowerMessage = messageContent.toLowerCase();

    // Check for project creation triggers
    const projectTriggers = [
      'build', 'create', 'make', 'develop', 'website', 'app', 'application',
      'project', 'code', 'program', 'software', 'site'
    ];

    const isProjectRequest = projectTriggers.some(trigger => 
      lowerMessage.includes(trigger)
    );

    if (isProjectRequest || lowerMessage.includes('i want to')) {
      await this.startRequirementsGathering(session);
    } else if (lowerMessage.includes('help')) {
      await this.sendHelpMessage(phoneNumber);
    } else if (lowerMessage.includes('status') || lowerMessage.includes('projects')) {
      await this.showUserProjects(session);
    } else {
      await this.sendWelcomeMessage(phoneNumber);
    }
  }

  async startRequirementsGathering(session) {
    const phoneNumber = session.whatsappNumber;
    
    await session.updateState('gathering_requirements', {
      currentStep: 'project_description'
    });

    const message = `🚀 Great! I'll help you build your project. Let's gather some details:

1️⃣ **What do you want to build?**
Please describe your project idea (e.g., "a portfolio website", "an e-commerce store", "a blog")`;

    await this.sendMessage(phoneNumber, message);
  }

  async sendWelcomeMessage(phoneNumber) {
    const message = `👋 Welcome to your AI Coding Assistant!

I can help you:
🔨 Build websites and applications
🌐 Purchase domains
☁️ Deploy to hosting platforms
📱 Get your project live in minutes

Just tell me what you want to build! For example:
• "I want to build a portfolio website"
• "Create an e-commerce store"
• "Make a blog for my business"

Type 'help' for more options.`;

    await this.sendMessage(phoneNumber, message);
  }

  async sendHelpMessage(phoneNumber) {
    const message = `🆘 **Help Menu**

**Commands:**
• "build [project]" - Start a new project
• "status" - Check your projects
• "help" - Show this menu

**What I can build:**
• Portfolio websites
• Business websites
• E-commerce stores
• Blogs
• Landing pages
• And much more!

**Process:**
1. Tell me what you want to build
2. I'll gather requirements
3. Generate your code
4. Help you get a domain
5. Deploy your site live

Ready to start? Just tell me what you want to build!`;

    await this.sendMessage(phoneNumber, message);
  }

  async sendMessage(phoneNumber, message, messageType = 'text') {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: phoneNumber,
        type: messageType,
        [messageType]: {
          body: message
        }
      };

      const response = await axios.post(`${this.apiUrl}/messages`, payload, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      this.logger.info('Message sent successfully', {
        to: phoneNumber,
        messageId: response.data.messages[0].id
      });

      return response.data;
    } catch (error) {
      this.logger.error('Error sending message:', error.response?.data || error.message);
      throw error;
    }
  }

  async sendDocument(phoneNumber, filePath, caption = '') {
    try {
      const form = new FormData();
      form.append('messaging_product', 'whatsapp');
      form.append('to', phoneNumber);
      form.append('type', 'document');
      form.append('document', fs.createReadStream(filePath));
      
      if (caption) {
        form.append('caption', caption);
      }

      const response = await axios.post(`${this.apiUrl}/messages`, form, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          ...form.getHeaders()
        }
      });

      this.logger.info('Document sent successfully', {
        to: phoneNumber,
        file: path.basename(filePath)
      });

      return response.data;
    } catch (error) {
      this.logger.error('Error sending document:', error.response?.data || error.message);
      throw error;
    }
  }

  async handleMessageStatus(statusData) {
    // Handle message delivery status updates
    this.logger.info('Message status update', statusData);
  }

  // Placeholder methods for other handlers
  async handleRequirementsGathering(session, messageContent) {
    // Will be implemented with AICodeAgent
    await this.aiAgent.handleRequirementsGathering(session, messageContent);
  }

  async handleRequirementsConfirmation(session, messageContent) {
    await this.aiAgent.handleRequirementsConfirmation(session, messageContent);
  }

  async handleCodeReady(session, messageContent) {
    await this.aiAgent.handleCodeReady(session, messageContent);
  }

  async handleDomainSelection(session, messageContent) {
    await this.domainHostingHandler.handleDomainSelection(session, messageContent);
  }

  async handleHostingSelection(session, messageContent) {
    await this.domainHostingHandler.handleHostingSelection(session, messageContent);
  }

  async handleUnknownState(session, messageContent) {
    const phoneNumber = session.whatsappNumber;
    await this.sendMessage(phoneNumber, 
      "I'm not sure how to help with that right now. Type 'help' to see what I can do!"
    );
    await session.updateState('idle');
  }

  async showUserProjects(session) {
    // Will be implemented to show user's projects
    const phoneNumber = session.whatsappNumber;
    await this.sendMessage(phoneNumber, 
      "📊 Your projects feature is coming soon! For now, you can start building a new project by telling me what you want to create."
    );
  }
}

module.exports = WhatsAppHandler;
