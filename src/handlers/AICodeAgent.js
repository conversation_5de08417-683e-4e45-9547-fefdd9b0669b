const Anthropic = require('@anthropic-ai/sdk');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const { Project } = require('../models');
const logger = require('../utils/logger');
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

class AICodeAgent {
  constructor() {
    this.anthropic = process.env.ANTHROPIC_API_KEY ? new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY
    }) : null;
    
    this.gemini = process.env.GOOGLE_AI_API_KEY ? new GoogleGenerativeAI(
      process.env.GOOGLE_AI_API_KEY
    ) : null;
    
    this.logger = logger.child({ module: 'AICodeAgent' });
    this.requirementsSteps = [
      'project_description',
      'features',
      'tech_stack',
      'design_preferences',
      'target_audience'
    ];
  }

  async handleRequirementsGathering(session, messageContent) {
    const phoneNumber = session.whatsappNumber;
    const currentStep = session.context.currentStep;
    
    try {
      // Process the current step
      await this.processRequirementStep(session, currentStep, messageContent);
      
      // Move to next step or complete gathering
      const nextStep = this.getNextRequirementStep(currentStep);
      
      if (nextStep) {
        await session.updateState('gathering_requirements', {
          currentStep: nextStep
        });
        await this.askNextRequirement(session, nextStep);
      } else {
        // All requirements gathered, show summary
        await session.updateState('confirming_requirements');
        await this.showRequirementsSummary(session);
      }
      
    } catch (error) {
      this.logger.error('Error in requirements gathering:', error);
      await this.sendMessage(phoneNumber, 
        "I had trouble processing that. Could you please try again?"
      );
    }
  }

  async processRequirementStep(session, step, messageContent) {
    const requirements = session.context.requirements || {};
    
    switch (step) {
      case 'project_description':
        requirements.description = messageContent;
        break;
      case 'features':
        requirements.features = this.parseFeatures(messageContent);
        break;
      case 'tech_stack':
        requirements.techStack = this.parseTechStack(messageContent);
        break;
      case 'design_preferences':
        requirements.designPreferences = messageContent;
        break;
      case 'target_audience':
        requirements.targetAudience = messageContent;
        break;
    }
    
    await session.updateRequirements(requirements);
  }

  parseFeatures(messageContent) {
    // Extract features from user input
    const features = [];
    const lowerContent = messageContent.toLowerCase();
    
    // Common feature keywords
    const featureMap = {
      'contact': 'Contact form',
      'gallery': 'Image gallery',
      'blog': 'Blog section',
      'shop': 'E-commerce functionality',
      'cart': 'Shopping cart',
      'payment': 'Payment processing',
      'login': 'User authentication',
      'search': 'Search functionality',
      'responsive': 'Mobile responsive design',
      'admin': 'Admin panel',
      'dashboard': 'User dashboard',
      'api': 'API integration',
      'database': 'Database integration'
    };
    
    Object.keys(featureMap).forEach(keyword => {
      if (lowerContent.includes(keyword)) {
        features.push(featureMap[keyword]);
      }
    });
    
    // If no specific features detected, use the raw input
    if (features.length === 0) {
      features.push(messageContent);
    }
    
    return features;
  }

  parseTechStack(messageContent) {
    const techStack = [];
    const lowerContent = messageContent.toLowerCase();
    
    // Technology mapping
    const techMap = {
      'react': 'React',
      'vue': 'Vue.js',
      'angular': 'Angular',
      'html': 'HTML/CSS/JavaScript',
      'wordpress': 'WordPress',
      'node': 'Node.js',
      'express': 'Express.js',
      'mongodb': 'MongoDB',
      'mysql': 'MySQL',
      'postgresql': 'PostgreSQL',
      'tailwind': 'Tailwind CSS',
      'bootstrap': 'Bootstrap',
      'typescript': 'TypeScript'
    };
    
    Object.keys(techMap).forEach(tech => {
      if (lowerContent.includes(tech)) {
        techStack.push(techMap[tech]);
      }
    });
    
    // Default to modern web stack if nothing specified
    if (techStack.length === 0) {
      techStack.push('HTML/CSS/JavaScript', 'React');
    }
    
    return techStack;
  }

  getNextRequirementStep(currentStep) {
    const currentIndex = this.requirementsSteps.indexOf(currentStep);
    return currentIndex < this.requirementsSteps.length - 1 
      ? this.requirementsSteps[currentIndex + 1] 
      : null;
  }

  async askNextRequirement(session, step) {
    const phoneNumber = session.whatsappNumber;
    let message = '';
    
    switch (step) {
      case 'features':
        message = `2️⃣ **What features do you want?**
Please list the main features (e.g., "contact form, image gallery, blog, shopping cart")`;
        break;
      case 'tech_stack':
        message = `3️⃣ **Any technology preferences?**
Do you have preferences for technologies like React, WordPress, etc.? (or say "no preference")`;
        break;
      case 'design_preferences':
        message = `4️⃣ **Design style preferences?**
Describe your preferred style (e.g., "modern and minimalist", "colorful and playful", "professional")`;
        break;
      case 'target_audience':
        message = `5️⃣ **Who is your target audience?**
Who will use this website? (e.g., "small business customers", "young professionals", "general public")`;
        break;
    }
    
    await this.sendMessage(phoneNumber, message);
  }

  async showRequirementsSummary(session) {
    const phoneNumber = session.whatsappNumber;
    const req = session.context.requirements;
    
    const summary = `📋 **Project Summary**

**Description:** ${req.description}
**Features:** ${req.features?.join(', ') || 'Basic functionality'}
**Tech Stack:** ${req.techStack?.join(', ') || 'Modern web technologies'}
**Design:** ${req.designPreferences || 'Clean and professional'}
**Audience:** ${req.targetAudience || 'General users'}

Does this look correct? Reply:
✅ "Yes" to start generating code
✏️ "Edit [section]" to modify (e.g., "edit features")
❌ "Start over" to begin again`;

    await this.sendMessage(phoneNumber, summary);
  }

  async handleRequirementsConfirmation(session, messageContent) {
    const phoneNumber = session.whatsappNumber;
    const lowerMessage = messageContent.toLowerCase();
    
    if (lowerMessage.includes('yes') || lowerMessage.includes('correct') || lowerMessage.includes('generate')) {
      await this.startCodeGeneration(session);
    } else if (lowerMessage.includes('edit')) {
      await this.handleRequirementEdit(session, messageContent);
    } else if (lowerMessage.includes('start over')) {
      await session.updateState('gathering_requirements', {
        currentStep: 'project_description',
        requirements: {}
      });
      await this.sendMessage(phoneNumber, 
        "Let's start over! What do you want to build?"
      );
    } else {
      await this.sendMessage(phoneNumber, 
        "Please reply with 'Yes' to generate code, 'Edit [section]' to modify, or 'Start over' to begin again."
      );
    }
  }

  async startCodeGeneration(session) {
    const phoneNumber = session.whatsappNumber;
    
    await session.updateState('generating_code');
    await this.sendMessage(phoneNumber, 
      "🔄 Perfect! I'm generating your code now. This may take a minute..."
    );
    
    try {
      const generatedCode = await this.generateCode(session.context.requirements);
      await session.setGeneratedCode(generatedCode);
      
      // Create project record
      const project = await Project.createProject(
        session.userId,
        session.sessionId,
        {
          name: this.generateProjectName(session.context.requirements),
          description: session.context.requirements.description,
          requirements: session.context.requirements,
          codebase: {
            files: generatedCode.files,
            framework: generatedCode.framework,
            dependencies: generatedCode.dependencies,
            readme: generatedCode.readme,
            buildInstructions: generatedCode.instructions
          },
          status: 'code_generated'
        }
      );
      
      await this.sendCodeReady(session, project);
      
    } catch (error) {
      this.logger.error('Error generating code:', error);
      await this.sendMessage(phoneNumber, 
        "❌ I encountered an error generating your code. Please try again or contact support."
      );
      await session.updateState('idle');
    }
  }

  async generateCode(requirements) {
    try {
      const prompt = this.buildCodeGenerationPrompt(requirements);
      
      let response;
      if (this.anthropic) {
        response = await this.generateWithClaude(prompt);
      } else if (this.gemini) {
        response = await this.generateWithGemini(prompt);
      } else {
        throw new Error('No AI API configured');
      }
      
      return this.parseCodeResponse(response);
      
    } catch (error) {
      this.logger.error('Error in code generation:', error);
      throw error;
    }
  }

  buildCodeGenerationPrompt(requirements) {
    return `Generate a complete, production-ready web application based on these requirements:

**Project Description:** ${requirements.description}
**Features:** ${requirements.features?.join(', ') || 'Basic functionality'}
**Tech Stack:** ${requirements.techStack?.join(', ') || 'HTML/CSS/JavaScript'}
**Design Style:** ${requirements.designPreferences || 'Clean and professional'}
**Target Audience:** ${requirements.targetAudience || 'General users'}

Please provide:
1. Complete file structure with all necessary files
2. Full code for each file (HTML, CSS, JavaScript, etc.)
3. Package.json if using Node.js/React
4. README with setup instructions
5. Deployment instructions

Make the code:
- Production-ready and well-commented
- Responsive and mobile-friendly
- Following best practices
- Including proper error handling
- SEO-optimized

Format your response as JSON with this structure:
{
  "framework": "framework_name",
  "files": [
    {
      "path": "file_path",
      "content": "file_content",
      "language": "file_language"
    }
  ],
  "dependencies": ["dependency1", "dependency2"],
  "instructions": "setup_and_deployment_instructions",
  "readme": "readme_content"
}`;
  }

  async generateWithClaude(prompt) {
    const response = await this.anthropic.messages.create({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 4000,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    });
    
    return response.content[0].text;
  }

  async generateWithGemini(prompt) {
    const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
    const result = await model.generateContent(prompt);
    const response = await result.response;
    return response.text();
  }

  parseCodeResponse(response) {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Fallback: create a basic structure
      return this.createFallbackCodeStructure(response);
      
    } catch (error) {
      this.logger.error('Error parsing code response:', error);
      return this.createFallbackCodeStructure(response);
    }
  }

  createFallbackCodeStructure(response) {
    return {
      framework: 'HTML/CSS/JavaScript',
      files: [
        {
          path: 'index.html',
          content: response.includes('<html>') ? response : this.generateBasicHTML(),
          language: 'html'
        },
        {
          path: 'style.css',
          content: this.generateBasicCSS(),
          language: 'css'
        },
        {
          path: 'script.js',
          content: this.generateBasicJS(),
          language: 'javascript'
        }
      ],
      dependencies: [],
      instructions: 'Open index.html in a web browser to view the website.',
      readme: '# Generated Website\n\nThis is your generated website. Open index.html to view it.'
    };
  }

  generateBasicHTML() {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Website</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <h1>Welcome to Your Website</h1>
    </header>
    <main>
        <section>
            <h2>About</h2>
            <p>This is your generated website. Customize it as needed!</p>
        </section>
    </main>
    <script src="script.js"></script>
</body>
</html>`;
  }

  generateBasicCSS() {
    return `* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

header {
    background: #007bff;
    color: white;
    text-align: center;
    padding: 1rem;
}

main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

section {
    margin-bottom: 2rem;
}`;
  }

  generateBasicJS() {
    return `// Your website JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Website loaded successfully!');
});`;
  }

  generateProjectName(requirements) {
    const description = requirements.description || 'Website';
    const words = description.split(' ').slice(0, 3);
    return words.map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  }

  async sendCodeReady(session, project) {
    const phoneNumber = session.whatsappNumber;
    
    const message = `🎉 **Your code is ready!**

**Project:** ${project.name}
**Files:** ${project.codebase.totalFiles} files generated
**Framework:** ${project.codebase.framework}

What would you like to do next?
📁 "Download code" - Get your files
🌐 "Get domain" - Purchase a domain name
☁️ "Deploy now" - Deploy to hosting
📖 "Show files" - See the code structure

Reply with your choice!`;

    await this.sendMessage(phoneNumber, message);
  }

  async handleCodeReady(session, messageContent) {
    const phoneNumber = session.whatsappNumber;
    const lowerMessage = messageContent.toLowerCase();
    
    if (lowerMessage.includes('download')) {
      await this.handleCodeDownload(session);
    } else if (lowerMessage.includes('domain')) {
      await this.startDomainProcess(session);
    } else if (lowerMessage.includes('deploy')) {
      await this.startDeploymentProcess(session);
    } else if (lowerMessage.includes('show') || lowerMessage.includes('files')) {
      await this.showCodeStructure(session);
    } else {
      await this.sendMessage(phoneNumber, 
        "Please choose: 'Download code', 'Get domain', 'Deploy now', or 'Show files'"
      );
    }
  }

  async sendMessage(phoneNumber, message) {
    // This will be handled by WhatsAppHandler
    const WhatsAppHandler = require('./WhatsAppHandler');
    const handler = new WhatsAppHandler();
    return handler.sendMessage(phoneNumber, message);
  }

  // Placeholder methods for other handlers
  async handleCodeDownload(session) {
    const phoneNumber = session.whatsappNumber;
    await this.sendMessage(phoneNumber, 
      "📁 Code download feature coming soon! For now, you can proceed with domain purchase or deployment."
    );
  }

  async startDomainProcess(session) {
    await session.updateState('selecting_domain');
    const phoneNumber = session.whatsappNumber;
    await this.sendMessage(phoneNumber, 
      "🌐 Great! Let's get you a domain. What domain name would you like? (e.g., 'myawesomesite.com')"
    );
  }

  async startDeploymentProcess(session) {
    await session.updateState('selecting_hosting');
    const phoneNumber = session.whatsappNumber;
    await this.sendMessage(phoneNumber, 
      "☁️ Let's deploy your site! Choose a hosting provider:\n\n1️⃣ Vercel (Free, fast)\n2️⃣ Netlify (Free, easy)\n3️⃣ DigitalOcean (Paid, powerful)\n\nReply with the number or name."
    );
  }

  async showCodeStructure(session) {
    const phoneNumber = session.whatsappNumber;
    const files = session.context.generatedCode?.files || [];
    
    const structure = files.map(file => 
      `📄 ${file.path} (${file.language})`
    ).join('\n');
    
    const message = `📁 **Your Code Structure:**

${structure}

Total files: ${files.length}

What would you like to do next?
🌐 "Get domain" - Purchase a domain
☁️ "Deploy now" - Deploy to hosting`;

    await this.sendMessage(phoneNumber, message);
  }
}

module.exports = AICodeAgent;
