const { User, Session } = require('../models');
const logger = require('../utils/logger');
const jwt = require('jsonwebtoken');

class UserManager {
  constructor() {
    this.logger = logger.child({ module: 'UserManager' });
  }

  async findOrCreateUser(whatsappNumber, name = null) {
    try {
      // Validate WhatsApp number format
      if (!this.isValidWhatsAppNumber(whatsappNumber)) {
        throw new Error('Invalid WhatsApp number format');
      }

      // Try to find existing user
      let user = await User.findByWhatsAppNumber(whatsappNumber);
      
      if (user) {
        // Update last activity
        user.lastActivity = new Date();
        await user.save();
        
        this.logger.info('Existing user found', {
          userId: user._id,
          whatsappNumber: user.whatsappNumber
        });
        
        return user;
      }

      // Create new user
      user = await User.createFromWhatsApp(whatsappNumber, name);
      
      this.logger.info('New user created', {
        userId: user._id,
        whatsappNumber: user.whatsappNumber
      });

      return user;
    } catch (error) {
      this.logger.error('Error finding or creating user:', error);
      throw error;
    }
  }

  async getUserById(userId) {
    try {
      const user = await User.findById(userId);
      if (!user || !user.isActive) {
        throw new Error('User not found or inactive');
      }
      return user;
    } catch (error) {
      this.logger.error('Error getting user by ID:', error);
      throw error;
    }
  }

  async updateUserProfile(userId, profileData) {
    try {
      const user = await this.getUserById(userId);
      
      // Update allowed fields
      const allowedFields = ['name', 'email', 'profile', 'preferences'];
      allowedFields.forEach(field => {
        if (profileData[field] !== undefined) {
          if (field === 'profile' || field === 'preferences') {
            user[field] = { ...user[field], ...profileData[field] };
          } else {
            user[field] = profileData[field];
          }
        }
      });

      await user.save();
      
      this.logger.info('User profile updated', {
        userId: user._id,
        updatedFields: Object.keys(profileData)
      });

      return user;
    } catch (error) {
      this.logger.error('Error updating user profile:', error);
      throw error;
    }
  }

  async getActiveSession(whatsappNumber) {
    try {
      const session = await Session.findActiveByWhatsApp(whatsappNumber);
      
      if (session && !session.isExpired()) {
        this.logger.info('Active session found', {
          sessionId: session.sessionId,
          whatsappNumber,
          state: session.state
        });
        return session;
      }

      if (session && session.isExpired()) {
        // Mark expired session as inactive
        session.isActive = false;
        await session.save();
        
        this.logger.info('Session expired and marked inactive', {
          sessionId: session.sessionId,
          whatsappNumber
        });
      }

      return null;
    } catch (error) {
      this.logger.error('Error getting active session:', error);
      throw error;
    }
  }

  async createSession(userId, whatsappNumber) {
    try {
      // Check for existing active sessions and deactivate them
      const existingSessions = await Session.find({
        whatsappNumber,
        isActive: true
      });

      for (const session of existingSessions) {
        session.isActive = false;
        await session.save();
      }

      // Create new session
      const newSession = await Session.createSession(userId, whatsappNumber);
      
      this.logger.info('New session created', {
        sessionId: newSession.sessionId,
        userId,
        whatsappNumber
      });

      return newSession;
    } catch (error) {
      this.logger.error('Error creating session:', error);
      throw error;
    }
  }

  async endSession(sessionId) {
    try {
      const session = await Session.findOne({ sessionId, isActive: true });
      
      if (!session) {
        throw new Error('Session not found or already inactive');
      }

      session.isActive = false;
      await session.save();
      
      this.logger.info('Session ended', {
        sessionId,
        whatsappNumber: session.whatsappNumber
      });

      return session;
    } catch (error) {
      this.logger.error('Error ending session:', error);
      throw error;
    }
  }

  async extendSession(sessionId, minutes = null) {
    try {
      const session = await Session.findOne({ sessionId, isActive: true });
      
      if (!session) {
        throw new Error('Session not found or inactive');
      }

      await session.extendSession(minutes);
      
      this.logger.info('Session extended', {
        sessionId,
        newExpiryTime: session.expiresAt
      });

      return session;
    } catch (error) {
      this.logger.error('Error extending session:', error);
      throw error;
    }
  }

  async getUserSessions(userId, limit = 10) {
    try {
      const sessions = await Session.find({ userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .select('sessionId state lastActivity createdAt isActive');

      return sessions;
    } catch (error) {
      this.logger.error('Error getting user sessions:', error);
      throw error;
    }
  }

  async cleanupExpiredSessions() {
    try {
      const result = await Session.cleanupExpiredSessions();
      
      this.logger.info('Expired sessions cleaned up', {
        modifiedCount: result.modifiedCount
      });

      return result;
    } catch (error) {
      this.logger.error('Error cleaning up expired sessions:', error);
      throw error;
    }
  }

  async generateUserToken(user) {
    try {
      const token = user.generateAuthToken();
      
      this.logger.info('User token generated', {
        userId: user._id,
        whatsappNumber: user.whatsappNumber
      });

      return token;
    } catch (error) {
      this.logger.error('Error generating user token:', error);
      throw error;
    }
  }

  async verifyUserToken(token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await this.getUserById(decoded.userId);
      
      return { user, decoded };
    } catch (error) {
      this.logger.error('Error verifying user token:', error);
      throw new Error('Invalid or expired token');
    }
  }

  async updateUserUsage(userId, incrementProjects = false, incrementDeployments = false) {
    try {
      const user = await this.getUserById(userId);
      
      if (incrementProjects) {
        await user.incrementUsage();
      }
      
      if (incrementDeployments) {
        user.usage.totalDeployments += 1;
        await user.save();
      }

      this.logger.info('User usage updated', {
        userId,
        totalProjects: user.usage.totalProjects,
        totalDeployments: user.usage.totalDeployments,
        monthlyUsed: user.usage.monthlyUsed
      });

      return user;
    } catch (error) {
      this.logger.error('Error updating user usage:', error);
      throw error;
    }
  }

  async checkUserQuota(userId) {
    try {
      const user = await this.getUserById(userId);
      
      const canCreate = user.canCreateProject();
      const remaining = user.usage.monthlyQuota - user.usage.monthlyUsed;
      
      return {
        canCreate,
        remaining,
        quota: user.usage.monthlyQuota,
        used: user.usage.monthlyUsed,
        plan: user.subscription.plan
      };
    } catch (error) {
      this.logger.error('Error checking user quota:', error);
      throw error;
    }
  }

  async resetUserMonthlyUsage(userId) {
    try {
      const user = await this.getUserById(userId);
      await user.resetMonthlyUsage();
      
      this.logger.info('User monthly usage reset', {
        userId,
        resetDate: user.usage.lastResetDate
      });

      return user;
    } catch (error) {
      this.logger.error('Error resetting user monthly usage:', error);
      throw error;
    }
  }

  isValidWhatsAppNumber(number) {
    // WhatsApp numbers should be in E.164 format: +[country code][number]
    const whatsappRegex = /^\+[1-9]\d{1,14}$/;
    return whatsappRegex.test(number);
  }

  async getUserStats(userId) {
    try {
      const user = await this.getUserById(userId);
      const sessions = await this.getUserSessions(userId, 5);
      
      return {
        user: {
          id: user._id,
          whatsappNumber: user.whatsappNumber,
          name: user.name,
          registrationDate: user.registrationDate,
          lastActivity: user.lastActivity,
          subscription: user.subscription,
          usage: user.usage
        },
        recentSessions: sessions
      };
    } catch (error) {
      this.logger.error('Error getting user stats:', error);
      throw error;
    }
  }
}

module.exports = UserManager;
