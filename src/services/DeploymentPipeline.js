const fs = require('fs').promises;
const path = require('path');
const archiver = require('archiver');
const axios = require('axios');
const FormData = require('form-data');
const { Deployment, Project } = require('../models');
const logger = require('../utils/logger');

class DeploymentPipeline {
  constructor() {
    this.logger = logger.child({ module: 'DeploymentPipeline' });
    this.tempDir = path.join(process.cwd(), 'temp');
    this.deploymentsDir = path.join(process.cwd(), 'deployments');
    this.initializeDirectories();
  }

  async initializeDirectories() {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
      await fs.mkdir(this.deploymentsDir, { recursive: true });
    } catch (error) {
      this.logger.error('Error creating directories:', error);
    }
  }

  async deployProject(projectId, provider, configuration = {}) {
    try {
      this.logger.info('Starting deployment', { projectId, provider });
      
      // Get project data
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }
      
      // Create deployment record
      const deployment = await Deployment.createDeployment(
        projectId,
        project.userId,
        provider,
        configuration
      );
      
      // Start deployment process
      await deployment.updateStatus('building');
      await deployment.addLog('info', 'Starting deployment process');
      
      // Prepare project files
      const projectPath = await this.prepareProjectFiles(project, deployment);
      
      // Build project if needed
      await this.buildProject(project, deployment, projectPath);
      
      // Deploy to provider
      const deploymentResult = await this.deployToProvider(
        project,
        deployment,
        provider,
        projectPath,
        configuration
      );
      
      // Update deployment status
      if (deploymentResult.success) {
        await deployment.updateStatus('deployed', {
          'deployment.url': deploymentResult.url,
          'deployment.deploymentId': deploymentResult.deploymentId
        });
        
        await deployment.addLog('info', `Deployment successful: ${deploymentResult.url}`);
        
        // Update project
        await project.setHosting({
          provider,
          deploymentUrl: deploymentResult.url,
          deploymentId: deploymentResult.deploymentId,
          status: 'deployed'
        });
        
      } else {
        await deployment.setError('DEPLOYMENT_FAILED', deploymentResult.error);
        await deployment.addLog('error', `Deployment failed: ${deploymentResult.error}`);
      }
      
      // Cleanup temporary files
      await this.cleanupTempFiles(projectPath);
      
      return deployment;
      
    } catch (error) {
      this.logger.error('Deployment error:', error);
      throw error;
    }
  }

  async prepareProjectFiles(project, deployment) {
    try {
      const projectPath = path.join(this.tempDir, `project-${project._id}-${Date.now()}`);
      await fs.mkdir(projectPath, { recursive: true });
      
      await deployment.addLog('info', 'Preparing project files');
      
      // Write all project files
      for (const file of project.codebase.files) {
        const filePath = path.join(projectPath, file.path);
        const fileDir = path.dirname(filePath);
        
        // Create directory if it doesn't exist
        await fs.mkdir(fileDir, { recursive: true });
        
        // Write file content
        await fs.writeFile(filePath, file.content, 'utf8');
      }
      
      // Create package.json if it doesn't exist and we have dependencies
      if (project.codebase.dependencies && project.codebase.dependencies.length > 0) {
        const packageJsonPath = path.join(projectPath, 'package.json');
        
        try {
          await fs.access(packageJsonPath);
        } catch {
          // package.json doesn't exist, create it
          const packageJson = {
            name: project.name.toLowerCase().replace(/\s+/g, '-'),
            version: '1.0.0',
            description: project.description,
            main: 'index.js',
            scripts: {
              start: 'node index.js',
              build: 'echo "No build step required"'
            },
            dependencies: {}
          };
          
          // Add dependencies
          project.codebase.dependencies.forEach(dep => {
            packageJson.dependencies[dep] = 'latest';
          });
          
          await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
        }
      }
      
      await deployment.addLog('info', `Project files prepared at ${projectPath}`);
      return projectPath;
      
    } catch (error) {
      this.logger.error('Error preparing project files:', error);
      throw error;
    }
  }

  async buildProject(project, deployment, projectPath) {
    try {
      const framework = project.codebase.framework;
      
      await deployment.addLog('info', `Building project with framework: ${framework}`);
      
      // Check if build is needed
      const needsBuild = ['React', 'Vue.js', 'Angular', 'Next.js'].includes(framework);
      
      if (!needsBuild) {
        await deployment.addLog('info', 'No build step required for this framework');
        return;
      }
      
      // For frameworks that need building, we'll simulate the build process
      // In a real implementation, you would run npm install and npm run build
      
      await deployment.addLog('info', 'Simulating build process...');
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate build time
      
      // Create a simple build output for static sites
      const buildDir = path.join(projectPath, 'build');
      await fs.mkdir(buildDir, { recursive: true });
      
      // Copy static files to build directory
      const indexHtml = project.codebase.files.find(f => f.path === 'index.html');
      if (indexHtml) {
        await fs.writeFile(path.join(buildDir, 'index.html'), indexHtml.content);
      }
      
      const cssFiles = project.codebase.files.filter(f => f.path.endsWith('.css'));
      for (const cssFile of cssFiles) {
        await fs.writeFile(path.join(buildDir, cssFile.path), cssFile.content);
      }
      
      const jsFiles = project.codebase.files.filter(f => f.path.endsWith('.js'));
      for (const jsFile of jsFiles) {
        await fs.writeFile(path.join(buildDir, jsFile.path), jsFile.content);
      }
      
      await deployment.addLog('info', 'Build completed successfully');
      
    } catch (error) {
      this.logger.error('Build error:', error);
      await deployment.addLog('error', `Build failed: ${error.message}`);
      throw error;
    }
  }

  async deployToProvider(project, deployment, provider, projectPath, configuration) {
    try {
      await deployment.updateStatus('deploying');
      await deployment.addLog('info', `Deploying to ${provider}`);
      
      switch (provider) {
        case 'vercel':
          return await this.deployToVercel(project, deployment, projectPath, configuration);
        case 'netlify':
          return await this.deployToNetlify(project, deployment, projectPath, configuration);
        case 'digitalocean':
          return await this.deployToDigitalOcean(project, deployment, projectPath, configuration);
        default:
          throw new Error(`Unsupported provider: ${provider}`);
      }
    } catch (error) {
      this.logger.error(`Deployment to ${provider} failed:`, error);
      return { success: false, error: error.message };
    }
  }

  async deployToVercel(project, deployment, projectPath, configuration) {
    try {
      const token = process.env.VERCEL_ACCESS_TOKEN;
      if (!token) {
        throw new Error('Vercel access token not configured');
      }
      
      await deployment.addLog('info', 'Preparing Vercel deployment');
      
      // Create deployment archive
      const archivePath = await this.createDeploymentArchive(projectPath, deployment);
      
      // Upload to Vercel
      const deploymentData = await this.uploadToVercel(project, archivePath, token, deployment);
      
      await deployment.addLog('info', `Vercel deployment created: ${deploymentData.url}`);
      
      return {
        success: true,
        url: `https://${deploymentData.url}`,
        deploymentId: deploymentData.id
      };
      
    } catch (error) {
      this.logger.error('Vercel deployment error:', error);
      return { success: false, error: error.message };
    }
  }

  async deployToNetlify(project, deployment, projectPath, configuration) {
    try {
      await deployment.addLog('info', 'Simulating Netlify deployment');
      
      // Simulate deployment time
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const mockUrl = `${project.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.netlify.app`;
      
      await deployment.addLog('info', `Netlify deployment completed: ${mockUrl}`);
      
      return {
        success: true,
        url: `https://${mockUrl}`,
        deploymentId: `netlify-${Date.now()}`
      };
      
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async deployToDigitalOcean(project, deployment, projectPath, configuration) {
    try {
      await deployment.addLog('info', 'Simulating DigitalOcean deployment');
      
      // Simulate deployment time
      await new Promise(resolve => setTimeout(resolve, 4000));
      
      const mockUrl = `${project.name.toLowerCase().replace(/\s+/g, '-')}.do.app`;
      
      await deployment.addLog('info', `DigitalOcean deployment completed: ${mockUrl}`);
      
      return {
        success: true,
        url: `https://${mockUrl}`,
        deploymentId: `do-${Date.now()}`
      };
      
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async createDeploymentArchive(projectPath, deployment) {
    try {
      const archivePath = path.join(this.deploymentsDir, `deployment-${Date.now()}.zip`);
      
      await deployment.addLog('info', 'Creating deployment archive');
      
      return new Promise((resolve, reject) => {
        const output = require('fs').createWriteStream(archivePath);
        const archive = archiver('zip', { zlib: { level: 9 } });
        
        output.on('close', () => {
          this.logger.info(`Archive created: ${archivePath} (${archive.pointer()} bytes)`);
          resolve(archivePath);
        });
        
        archive.on('error', (err) => {
          reject(err);
        });
        
        archive.pipe(output);
        archive.directory(projectPath, false);
        archive.finalize();
      });
      
    } catch (error) {
      this.logger.error('Error creating archive:', error);
      throw error;
    }
  }

  async uploadToVercel(project, archivePath, token, deployment) {
    try {
      // This is a simplified version - real Vercel API requires more complex file handling
      await deployment.addLog('info', 'Uploading to Vercel');
      
      // Simulate upload and deployment
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockDeployment = {
        id: `vercel-${Date.now()}`,
        url: `${project.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.vercel.app`
      };
      
      return mockDeployment;
      
    } catch (error) {
      this.logger.error('Vercel upload error:', error);
      throw error;
    }
  }

  async cleanupTempFiles(projectPath) {
    try {
      await fs.rmdir(projectPath, { recursive: true });
      this.logger.info(`Cleaned up temporary files: ${projectPath}`);
    } catch (error) {
      this.logger.warn('Error cleaning up temp files:', error);
    }
  }

  async getDeploymentStatus(deploymentId) {
    try {
      const deployment = await Deployment.findOne({ deploymentId });
      if (!deployment) {
        throw new Error('Deployment not found');
      }
      
      return {
        status: deployment.status,
        url: deployment.deployment.url,
        logs: deployment.logs.slice(-10), // Last 10 logs
        createdAt: deployment.createdAt,
        completedAt: deployment.completedAt
      };
      
    } catch (error) {
      this.logger.error('Error getting deployment status:', error);
      throw error;
    }
  }

  async retryDeployment(deploymentId) {
    try {
      const deployment = await Deployment.findOne({ deploymentId });
      if (!deployment) {
        throw new Error('Deployment not found');
      }
      
      const project = await Project.findById(deployment.projectId);
      if (!project) {
        throw new Error('Project not found');
      }
      
      await deployment.retry('Manual retry requested');
      
      // Start new deployment
      return await this.deployProject(
        project._id,
        deployment.provider,
        deployment.configuration
      );
      
    } catch (error) {
      this.logger.error('Error retrying deployment:', error);
      throw error;
    }
  }
}

module.exports = DeploymentPipeline;
