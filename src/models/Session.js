const mongoose = require('mongoose');

const sessionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  whatsappNumber: {
    type: String,
    required: true,
    index: true
  },
  sessionId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  state: {
    type: String,
    enum: [
      'idle',
      'gathering_requirements',
      'confirming_requirements',
      'generating_code',
      'code_ready',
      'selecting_domain',
      'purchasing_domain',
      'selecting_hosting',
      'deploying',
      'deployed',
      'error'
    ],
    default: 'idle'
  },
  context: {
    currentStep: {
      type: String,
      default: 'initial'
    },
    projectType: String,
    requirements: {
      description: String,
      features: [String],
      techStack: [String],
      designPreferences: String,
      targetAudience: String,
      additionalNotes: String
    },
    generatedCode: {
      files: [{
        path: String,
        content: String,
        language: String
      }],
      framework: String,
      dependencies: [String],
      instructions: String
    },
    domain: {
      searchQuery: String,
      selectedDomain: String,
      price: Number,
      registrar: String,
      status: String
    },
    hosting: {
      provider: String,
      plan: String,
      deploymentUrl: String,
      status: String
    }
  },
  messages: [{
    messageId: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    direction: {
      type: String,
      enum: ['incoming', 'outgoing'],
      required: true
    },
    content: {
      type: String,
      required: true
    },
    messageType: {
      type: String,
      enum: ['text', 'image', 'document', 'audio'],
      default: 'text'
    },
    metadata: mongoose.Schema.Types.Mixed
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  lastActivity: {
    type: Date,
    default: Date.now
  },
  expiresAt: {
    type: Date,
    default: function() {
      return new Date(Date.now() + (parseInt(process.env.SESSION_TIMEOUT_MINUTES) || 30) * 60 * 1000);
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
sessionSchema.index({ whatsappNumber: 1, isActive: 1 });
sessionSchema.index({ userId: 1, isActive: 1 });
sessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });
sessionSchema.index({ lastActivity: 1 });

// Instance methods
sessionSchema.methods.addMessage = function(direction, content, messageType = 'text', metadata = {}) {
  this.messages.push({
    messageId: require('uuid').v4(),
    direction,
    content,
    messageType,
    metadata,
    timestamp: new Date()
  });
  this.lastActivity = new Date();
  this.expiresAt = new Date(Date.now() + (parseInt(process.env.SESSION_TIMEOUT_MINUTES) || 30) * 60 * 1000);
  return this.save();
};

sessionSchema.methods.updateState = function(newState, contextUpdates = {}) {
  this.state = newState;
  this.context = { ...this.context, ...contextUpdates };
  this.lastActivity = new Date();
  this.expiresAt = new Date(Date.now() + (parseInt(process.env.SESSION_TIMEOUT_MINUTES) || 30) * 60 * 1000);
  return this.save();
};

sessionSchema.methods.updateRequirements = function(requirements) {
  this.context.requirements = { ...this.context.requirements, ...requirements };
  this.lastActivity = new Date();
  return this.save();
};

sessionSchema.methods.setGeneratedCode = function(codeData) {
  this.context.generatedCode = codeData;
  this.state = 'code_ready';
  this.lastActivity = new Date();
  return this.save();
};

sessionSchema.methods.isExpired = function() {
  return new Date() > this.expiresAt;
};

sessionSchema.methods.extendSession = function(minutes = null) {
  const extensionMinutes = minutes || parseInt(process.env.SESSION_TIMEOUT_MINUTES) || 30;
  this.expiresAt = new Date(Date.now() + extensionMinutes * 60 * 1000);
  this.lastActivity = new Date();
  return this.save();
};

// Static methods
sessionSchema.statics.findActiveByWhatsApp = function(whatsappNumber) {
  return this.findOne({ 
    whatsappNumber, 
    isActive: true,
    expiresAt: { $gt: new Date() }
  }).populate('userId');
};

sessionSchema.statics.createSession = function(userId, whatsappNumber) {
  const sessionId = require('uuid').v4();
  return this.create({
    userId,
    whatsappNumber,
    sessionId,
    state: 'idle',
    context: {
      currentStep: 'initial'
    }
  });
};

sessionSchema.statics.cleanupExpiredSessions = function() {
  return this.updateMany(
    { expiresAt: { $lt: new Date() } },
    { isActive: false }
  );
};

// Pre-save middleware
sessionSchema.pre('save', function(next) {
  this.lastActivity = new Date();
  next();
});

module.exports = mongoose.model('Session', sessionSchema);
