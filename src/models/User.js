const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const userSchema = new mongoose.Schema({
  whatsappNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    validate: {
      validator: function(v) {
        return /^\+[1-9]\d{1,14}$/.test(v);
      },
      message: 'Invalid WhatsApp number format'
    }
  },
  name: {
    type: String,
    trim: true,
    maxlength: 100
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(v) {
        return !v || /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(v);
      },
      message: 'Invalid email format'
    }
  },
  profile: {
    preferredLanguage: {
      type: String,
      enum: ['javascript', 'python', 'react', 'nodejs', 'html', 'css', 'php', 'java', 'other'],
      default: 'javascript'
    },
    experienceLevel: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced'],
      default: 'beginner'
    },
    timezone: {
      type: String,
      default: 'UTC'
    }
  },
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'basic', 'premium'],
      default: 'free'
    },
    startDate: {
      type: Date,
      default: Date.now
    },
    endDate: Date,
    isActive: {
      type: Boolean,
      default: true
    }
  },
  usage: {
    totalProjects: {
      type: Number,
      default: 0
    },
    totalDeployments: {
      type: Number,
      default: 0
    },
    monthlyQuota: {
      type: Number,
      default: 5
    },
    monthlyUsed: {
      type: Number,
      default: 0
    },
    lastResetDate: {
      type: Date,
      default: Date.now
    }
  },
  preferences: {
    notifications: {
      type: Boolean,
      default: true
    },
    autoDeployment: {
      type: Boolean,
      default: false
    },
    defaultHostingProvider: {
      type: String,
      enum: ['vercel', 'digitalocean', 'hostinger'],
      default: 'vercel'
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastActivity: {
    type: Date,
    default: Date.now
  },
  registrationDate: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
userSchema.index({ whatsappNumber: 1 });
userSchema.index({ email: 1 });
userSchema.index({ 'subscription.plan': 1 });
userSchema.index({ lastActivity: 1 });

// Virtual for full name
userSchema.virtual('displayName').get(function() {
  return this.name || this.whatsappNumber;
});

// Instance methods
userSchema.methods.generateAuthToken = function() {
  const token = jwt.sign(
    { 
      userId: this._id, 
      whatsappNumber: this.whatsappNumber 
    },
    process.env.JWT_SECRET,
    { expiresIn: '7d' }
  );
  return token;
};

userSchema.methods.canCreateProject = function() {
  return this.usage.monthlyUsed < this.usage.monthlyQuota;
};

userSchema.methods.incrementUsage = function() {
  this.usage.monthlyUsed += 1;
  this.usage.totalProjects += 1;
  this.lastActivity = new Date();
  return this.save();
};

userSchema.methods.resetMonthlyUsage = function() {
  this.usage.monthlyUsed = 0;
  this.usage.lastResetDate = new Date();
  return this.save();
};

// Static methods
userSchema.statics.findByWhatsAppNumber = function(number) {
  return this.findOne({ whatsappNumber: number, isActive: true });
};

userSchema.statics.createFromWhatsApp = function(whatsappNumber, name = null) {
  return this.create({
    whatsappNumber,
    name,
    lastActivity: new Date()
  });
};

// Pre-save middleware
userSchema.pre('save', function(next) {
  this.lastActivity = new Date();
  next();
});

module.exports = mongoose.model('User', userSchema);
