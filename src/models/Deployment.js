const mongoose = require('mongoose');

const deploymentSchema = new mongoose.Schema({
  projectId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project',
    required: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  deploymentId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  provider: {
    type: String,
    enum: ['vercel', 'digitalocean', 'hostinger', 'netlify'],
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'building', 'deploying', 'deployed', 'failed', 'cancelled'],
    default: 'pending'
  },
  configuration: {
    buildCommand: String,
    outputDirectory: String,
    nodeVersion: String,
    environmentVariables: [{
      key: String,
      value: String,
      isSecret: {
        type: Boolean,
        default: false
      }
    }],
    customDomain: String,
    framework: String
  },
  deployment: {
    url: String,
    previewUrl: String,
    buildId: String,
    commitHash: String,
    branch: String,
    buildTime: Number,
    deployTime: Number,
    size: Number
  },
  logs: [{
    timestamp: {
      type: Date,
      default: Date.now
    },
    level: {
      type: String,
      enum: ['info', 'warn', 'error', 'debug'],
      default: 'info'
    },
    message: String,
    source: {
      type: String,
      enum: ['build', 'deploy', 'runtime'],
      default: 'deploy'
    }
  }],
  metrics: {
    buildDuration: Number,
    deploymentDuration: Number,
    totalDuration: Number,
    filesDeployed: Number,
    bundleSize: Number,
    firstLoadTime: Number
  },
  error: {
    code: String,
    message: String,
    details: String,
    timestamp: Date,
    retryCount: {
      type: Number,
      default: 0
    }
  },
  retries: [{
    timestamp: Date,
    reason: String,
    status: String
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  startedAt: {
    type: Date,
    default: Date.now
  },
  completedAt: Date,
  lastHealthCheck: Date,
  healthStatus: {
    type: String,
    enum: ['healthy', 'unhealthy', 'unknown'],
    default: 'unknown'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
deploymentSchema.index({ projectId: 1, status: 1 });
deploymentSchema.index({ userId: 1, createdAt: -1 });
deploymentSchema.index({ provider: 1, status: 1 });
deploymentSchema.index({ 'deployment.url': 1 });
deploymentSchema.index({ startedAt: -1 });

// Virtual for duration
deploymentSchema.virtual('duration').get(function() {
  if (this.completedAt && this.startedAt) {
    return this.completedAt - this.startedAt;
  }
  return null;
});

// Virtual for is completed
deploymentSchema.virtual('isCompleted').get(function() {
  return ['deployed', 'failed', 'cancelled'].includes(this.status);
});

// Instance methods
deploymentSchema.methods.addLog = function(level, message, source = 'deploy') {
  this.logs.push({
    level,
    message,
    source,
    timestamp: new Date()
  });
  return this.save();
};

deploymentSchema.methods.updateStatus = function(newStatus, additionalData = {}) {
  this.status = newStatus;
  
  if (newStatus === 'deployed') {
    this.completedAt = new Date();
    this.healthStatus = 'healthy';
  } else if (newStatus === 'failed' || newStatus === 'cancelled') {
    this.completedAt = new Date();
    this.healthStatus = 'unhealthy';
  }
  
  // Update any additional fields
  Object.keys(additionalData).forEach(key => {
    if (this.schema.paths[key]) {
      this[key] = additionalData[key];
    }
  });
  
  return this.save();
};

deploymentSchema.methods.setError = function(code, message, details = null) {
  this.error = {
    code,
    message,
    details,
    timestamp: new Date(),
    retryCount: this.error ? this.error.retryCount : 0
  };
  this.status = 'failed';
  this.completedAt = new Date();
  return this.save();
};

deploymentSchema.methods.retry = function(reason = 'Manual retry') {
  this.retries.push({
    timestamp: new Date(),
    reason,
    status: this.status
  });
  
  if (this.error) {
    this.error.retryCount += 1;
  }
  
  this.status = 'pending';
  this.completedAt = null;
  this.startedAt = new Date();
  
  return this.save();
};

deploymentSchema.methods.updateMetrics = function(metrics) {
  this.metrics = { ...this.metrics, ...metrics };
  return this.save();
};

deploymentSchema.methods.updateHealthStatus = function(status) {
  this.healthStatus = status;
  this.lastHealthCheck = new Date();
  return this.save();
};

// Static methods
deploymentSchema.statics.findByProject = function(projectId) {
  return this.find({ projectId, isActive: true }).sort({ createdAt: -1 });
};

deploymentSchema.statics.findByUser = function(userId, limit = 10) {
  return this.find({ userId, isActive: true })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('projectId', 'name description');
};

deploymentSchema.statics.findActiveDeployments = function() {
  return this.find({ 
    status: { $in: ['pending', 'building', 'deploying'] },
    isActive: true 
  });
};

deploymentSchema.statics.createDeployment = function(projectId, userId, provider, configuration = {}) {
  const deploymentId = require('uuid').v4();
  return this.create({
    projectId,
    userId,
    deploymentId,
    provider,
    configuration,
    startedAt: new Date()
  });
};

deploymentSchema.statics.getDeploymentStats = function(userId = null) {
  const matchStage = userId ? { userId: new mongoose.Types.ObjectId(userId) } : {};
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalDeployments: { $sum: 1 },
        successfulDeployments: {
          $sum: { $cond: [{ $eq: ['$status', 'deployed'] }, 1, 0] }
        },
        failedDeployments: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        },
        averageBuildTime: { $avg: '$metrics.buildDuration' },
        averageDeployTime: { $avg: '$metrics.deploymentDuration' }
      }
    }
  ]);
};

// Pre-save middleware
deploymentSchema.pre('save', function(next) {
  if (this.isModified('status') && this.isCompleted && !this.completedAt) {
    this.completedAt = new Date();
  }
  next();
});

module.exports = mongoose.model('Deployment', deploymentSchema);
