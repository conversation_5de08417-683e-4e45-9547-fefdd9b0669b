const mongoose = require('mongoose');

const projectSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  sessionId: {
    type: String,
    required: true,
    index: true
  },
  projectId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    maxlength: 1000
  },
  requirements: {
    originalRequest: String,
    features: [String],
    techStack: [String],
    framework: String,
    designPreferences: String,
    targetAudience: String,
    additionalNotes: String
  },
  codebase: {
    files: [{
      path: {
        type: String,
        required: true
      },
      content: {
        type: String,
        required: true
      },
      language: String,
      size: Number,
      lastModified: {
        type: Date,
        default: Date.now
      }
    }],
    framework: String,
    dependencies: [String],
    packageJson: String,
    readme: String,
    buildInstructions: String,
    totalFiles: {
      type: Number,
      default: 0
    },
    totalSize: {
      type: Number,
      default: 0
    }
  },
  domain: {
    name: String,
    registrar: String,
    purchaseDate: Date,
    expiryDate: Date,
    price: Number,
    status: {
      type: String,
      enum: ['pending', 'active', 'expired', 'cancelled'],
      default: 'pending'
    },
    nameservers: [String]
  },
  hosting: {
    provider: {
      type: String,
      enum: ['vercel', 'digitalocean', 'hostinger', 'netlify'],
      required: true
    },
    plan: String,
    deploymentId: String,
    deploymentUrl: String,
    repositoryUrl: String,
    status: {
      type: String,
      enum: ['pending', 'deploying', 'deployed', 'failed', 'stopped'],
      default: 'pending'
    },
    deploymentDate: Date,
    lastDeployment: Date,
    buildLogs: [String],
    environmentVariables: [{
      key: String,
      value: String,
      isSecret: {
        type: Boolean,
        default: false
      }
    }]
  },
  status: {
    type: String,
    enum: ['draft', 'code_generated', 'domain_purchased', 'deployed', 'live', 'archived'],
    default: 'draft'
  },
  metadata: {
    aiModel: String,
    generationTime: Number,
    codeQuality: {
      score: Number,
      issues: [String]
    },
    deploymentTime: Number,
    lastAccessed: {
      type: Date,
      default: Date.now
    },
    accessCount: {
      type: Number,
      default: 0
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  tags: [String]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
projectSchema.index({ userId: 1, status: 1 });
projectSchema.index({ 'domain.name': 1 });
projectSchema.index({ 'hosting.deploymentUrl': 1 });
projectSchema.index({ createdAt: -1 });
projectSchema.index({ 'metadata.lastAccessed': -1 });

// Virtual for live URL
projectSchema.virtual('liveUrl').get(function() {
  if (this.hosting.deploymentUrl) {
    return this.hosting.deploymentUrl;
  }
  if (this.domain.name && this.hosting.status === 'deployed') {
    return `https://${this.domain.name}`;
  }
  return null;
});

// Virtual for project size
projectSchema.virtual('projectSize').get(function() {
  return this.codebase.totalSize || 0;
});

// Instance methods
projectSchema.methods.addFile = function(path, content, language = null) {
  const existingFileIndex = this.codebase.files.findIndex(file => file.path === path);
  const fileSize = Buffer.byteLength(content, 'utf8');
  
  if (existingFileIndex !== -1) {
    // Update existing file
    this.codebase.files[existingFileIndex].content = content;
    this.codebase.files[existingFileIndex].language = language;
    this.codebase.files[existingFileIndex].size = fileSize;
    this.codebase.files[existingFileIndex].lastModified = new Date();
  } else {
    // Add new file
    this.codebase.files.push({
      path,
      content,
      language,
      size: fileSize,
      lastModified: new Date()
    });
  }
  
  this.updateCodebaseStats();
  return this.save();
};

projectSchema.methods.updateCodebaseStats = function() {
  this.codebase.totalFiles = this.codebase.files.length;
  this.codebase.totalSize = this.codebase.files.reduce((total, file) => total + (file.size || 0), 0);
};

projectSchema.methods.updateStatus = function(newStatus) {
  this.status = newStatus;
  this.metadata.lastAccessed = new Date();
  return this.save();
};

projectSchema.methods.setDomain = function(domainData) {
  this.domain = { ...this.domain, ...domainData };
  if (domainData.status === 'active') {
    this.status = 'domain_purchased';
  }
  return this.save();
};

projectSchema.methods.setHosting = function(hostingData) {
  this.hosting = { ...this.hosting, ...hostingData };
  if (hostingData.status === 'deployed') {
    this.status = 'deployed';
    this.hosting.deploymentDate = new Date();
  }
  return this.save();
};

projectSchema.methods.incrementAccess = function() {
  this.metadata.accessCount += 1;
  this.metadata.lastAccessed = new Date();
  return this.save();
};

// Static methods
projectSchema.statics.findByUser = function(userId, status = null) {
  const query = { userId, isActive: true };
  if (status) {
    query.status = status;
  }
  return this.find(query).sort({ createdAt: -1 });
};

projectSchema.statics.findByDomain = function(domainName) {
  return this.findOne({ 'domain.name': domainName, isActive: true });
};

projectSchema.statics.createProject = function(userId, sessionId, projectData) {
  const projectId = require('uuid').v4();
  return this.create({
    userId,
    sessionId,
    projectId,
    ...projectData,
    metadata: {
      ...projectData.metadata,
      lastAccessed: new Date(),
      accessCount: 0
    }
  });
};

// Pre-save middleware
projectSchema.pre('save', function(next) {
  if (this.isModified('codebase.files')) {
    this.updateCodebaseStats();
  }
  next();
});

module.exports = mongoose.model('Project', projectSchema);
