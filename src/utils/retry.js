const logger = require('./logger');

class RetryUtils {
  constructor() {
    this.defaultOptions = {
      maxAttempts: 3,
      baseDelay: 1000, // 1 second
      maxDelay: 30000, // 30 seconds
      backoffFactor: 2,
      jitter: true,
      retryCondition: (error) => true
    };
  }

  async withRetry(operation, options = {}) {
    const config = { ...this.defaultOptions, ...options };
    let lastError;

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        logger.debug(`Attempt ${attempt}/${config.maxAttempts}`, {
          operation: operation.name || 'anonymous'
        });

        const result = await operation();
        
        if (attempt > 1) {
          logger.info(`Operation succeeded on attempt ${attempt}`, {
            operation: operation.name || 'anonymous'
          });
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        logger.warn(`Attempt ${attempt} failed`, {
          operation: operation.name || 'anonymous',
          error: error.message,
          attempt,
          maxAttempts: config.maxAttempts
        });

        // Check if we should retry this error
        if (!config.retryCondition(error)) {
          logger.info('Error not retryable, stopping attempts', {
            operation: operation.name || 'anonymous',
            error: error.message
          });
          throw error;
        }

        // Don't wait after the last attempt
        if (attempt === config.maxAttempts) {
          break;
        }

        // Calculate delay for next attempt
        const delay = this.calculateDelay(attempt, config);
        
        logger.debug(`Waiting ${delay}ms before next attempt`, {
          operation: operation.name || 'anonymous',
          attempt: attempt + 1
        });

        await this.sleep(delay);
      }
    }

    logger.error(`All ${config.maxAttempts} attempts failed`, {
      operation: operation.name || 'anonymous',
      finalError: lastError.message
    });

    throw lastError;
  }

  calculateDelay(attempt, config) {
    // Exponential backoff: baseDelay * (backoffFactor ^ (attempt - 1))
    let delay = config.baseDelay * Math.pow(config.backoffFactor, attempt - 1);
    
    // Cap at maxDelay
    delay = Math.min(delay, config.maxDelay);
    
    // Add jitter to prevent thundering herd
    if (config.jitter) {
      const jitterAmount = delay * 0.1; // 10% jitter
      const jitter = (Math.random() - 0.5) * 2 * jitterAmount;
      delay += jitter;
    }
    
    return Math.max(0, Math.floor(delay));
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Predefined retry conditions
  static retryConditions = {
    // Retry on network errors
    networkErrors: (error) => {
      const networkErrorCodes = ['ECONNRESET', 'ENOTFOUND', 'ECONNREFUSED', 'ETIMEDOUT'];
      return networkErrorCodes.includes(error.code) || 
             error.message.includes('network') ||
             error.message.includes('timeout');
    },

    // Retry on HTTP 5xx errors
    serverErrors: (error) => {
      return error.response && error.response.status >= 500;
    },

    // Retry on rate limit errors
    rateLimitErrors: (error) => {
      return error.response && error.response.status === 429;
    },

    // Retry on temporary database errors
    databaseErrors: (error) => {
      const dbErrorMessages = [
        'connection timeout',
        'connection refused',
        'connection reset',
        'server selection timeout',
        'topology was destroyed'
      ];
      return dbErrorMessages.some(msg => 
        error.message.toLowerCase().includes(msg)
      );
    },

    // Retry on WhatsApp API errors
    whatsappErrors: (error) => {
      if (!error.response) return false;
      
      const retryableStatuses = [429, 500, 502, 503, 504];
      const retryableErrorCodes = [
        'rate_limit_hit',
        'temporary_error',
        'server_error'
      ];
      
      return retryableStatuses.includes(error.response.status) ||
             (error.response.data && 
              retryableErrorCodes.includes(error.response.data.error?.code));
    },

    // Retry on AI API errors
    aiApiErrors: (error) => {
      if (!error.response) return false;
      
      const retryableStatuses = [429, 500, 502, 503, 504];
      const retryableMessages = [
        'rate limit',
        'server error',
        'service unavailable',
        'timeout'
      ];
      
      return retryableStatuses.includes(error.response.status) ||
             retryableMessages.some(msg => 
               error.message.toLowerCase().includes(msg)
             );
    },

    // Combined condition for common retryable errors
    common: (error) => {
      return RetryUtils.retryConditions.networkErrors(error) ||
             RetryUtils.retryConditions.serverErrors(error) ||
             RetryUtils.retryConditions.rateLimitErrors(error);
    }
  };

  // Convenience methods for common scenarios
  async retryNetworkOperation(operation, maxAttempts = 3) {
    return this.withRetry(operation, {
      maxAttempts,
      retryCondition: RetryUtils.retryConditions.networkErrors
    });
  }

  async retryApiCall(operation, maxAttempts = 3) {
    return this.withRetry(operation, {
      maxAttempts,
      baseDelay: 2000,
      maxDelay: 60000,
      retryCondition: RetryUtils.retryConditions.common
    });
  }

  async retryDatabaseOperation(operation, maxAttempts = 5) {
    return this.withRetry(operation, {
      maxAttempts,
      baseDelay: 500,
      maxDelay: 10000,
      retryCondition: RetryUtils.retryConditions.databaseErrors
    });
  }

  async retryWhatsAppCall(operation, maxAttempts = 3) {
    return this.withRetry(operation, {
      maxAttempts,
      baseDelay: 5000, // WhatsApp rate limits are strict
      maxDelay: 120000, // 2 minutes
      retryCondition: RetryUtils.retryConditions.whatsappErrors
    });
  }

  async retryAIApiCall(operation, maxAttempts = 3) {
    return this.withRetry(operation, {
      maxAttempts,
      baseDelay: 3000,
      maxDelay: 180000, // 3 minutes for AI APIs
      retryCondition: RetryUtils.retryConditions.aiApiErrors
    });
  }

  // Circuit breaker pattern
  createCircuitBreaker(operation, options = {}) {
    const config = {
      failureThreshold: 5,
      resetTimeout: 60000, // 1 minute
      monitoringPeriod: 60000, // 1 minute
      ...options
    };

    let state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    let failures = 0;
    let lastFailureTime = null;
    let successCount = 0;

    return async (...args) => {
      const now = Date.now();

      // Reset failure count if monitoring period has passed
      if (lastFailureTime && (now - lastFailureTime) > config.monitoringPeriod) {
        failures = 0;
      }

      // Check if circuit should move from OPEN to HALF_OPEN
      if (state === 'OPEN' && (now - lastFailureTime) > config.resetTimeout) {
        state = 'HALF_OPEN';
        successCount = 0;
        logger.info('Circuit breaker moving to HALF_OPEN state');
      }

      // Reject immediately if circuit is OPEN
      if (state === 'OPEN') {
        throw new Error('Circuit breaker is OPEN - operation rejected');
      }

      try {
        const result = await operation(...args);

        // Success - handle state transitions
        if (state === 'HALF_OPEN') {
          successCount++;
          if (successCount >= 3) { // Require 3 successes to close
            state = 'CLOSED';
            failures = 0;
            logger.info('Circuit breaker CLOSED after successful recovery');
          }
        } else if (state === 'CLOSED') {
          failures = 0; // Reset failure count on success
        }

        return result;
      } catch (error) {
        failures++;
        lastFailureTime = now;

        logger.warn('Circuit breaker recorded failure', {
          failures,
          threshold: config.failureThreshold,
          state
        });

        // Open circuit if failure threshold reached
        if (failures >= config.failureThreshold) {
          state = 'OPEN';
          logger.error('Circuit breaker OPENED due to repeated failures');
        } else if (state === 'HALF_OPEN') {
          state = 'OPEN';
          logger.error('Circuit breaker returned to OPEN from HALF_OPEN');
        }

        throw error;
      }
    };
  }
}

module.exports = new RetryUtils();
