const crypto = require('crypto');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const rateLimit = require('express-rate-limit');
const logger = require('./logger');

class SecurityUtils {
  constructor() {
    this.encryptionKey = process.env.ENCRYPTION_KEY;
    this.jwtSecret = process.env.JWT_SECRET;
    this.algorithm = 'aes-256-gcm';
  }

  // Encryption/Decryption
  encrypt(text) {
    try {
      if (!this.encryptionKey) {
        throw new Error('Encryption key not configured');
      }

      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(this.algorithm, this.encryptionKey);
      cipher.setAAD(Buffer.from('additional-data'));
      
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return {
        encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex')
      };
    } catch (error) {
      logger.error('Encryption error:', error);
      throw new Error('Encryption failed');
    }
  }

  decrypt(encryptedData) {
    try {
      if (!this.encryptionKey) {
        throw new Error('Encryption key not configured');
      }

      const { encrypted, iv, authTag } = encryptedData;
      const decipher = crypto.createDecipher(this.algorithm, this.encryptionKey);
      
      decipher.setAAD(Buffer.from('additional-data'));
      decipher.setAuthTag(Buffer.from(authTag, 'hex'));
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      logger.error('Decryption error:', error);
      throw new Error('Decryption failed');
    }
  }

  // Password hashing
  async hashPassword(password) {
    try {
      const saltRounds = 12;
      return await bcrypt.hash(password, saltRounds);
    } catch (error) {
      logger.error('Password hashing error:', error);
      throw new Error('Password hashing failed');
    }
  }

  async verifyPassword(password, hashedPassword) {
    try {
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      logger.error('Password verification error:', error);
      throw new Error('Password verification failed');
    }
  }

  // JWT token utilities
  generateToken(payload, expiresIn = '7d') {
    try {
      return jwt.sign(payload, this.jwtSecret, { expiresIn });
    } catch (error) {
      logger.error('Token generation error:', error);
      throw new Error('Token generation failed');
    }
  }

  verifyToken(token) {
    try {
      return jwt.verify(token, this.jwtSecret);
    } catch (error) {
      logger.error('Token verification error:', error);
      throw new Error('Invalid or expired token');
    }
  }

  // Input validation and sanitization
  sanitizeInput(input) {
    if (typeof input !== 'string') {
      return input;
    }

    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }

  validateWhatsAppNumber(phoneNumber) {
    const whatsappRegex = /^\+[1-9]\d{1,14}$/;
    return whatsappRegex.test(phoneNumber);
  }

  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  validateDomainName(domain) {
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
  }

  validateProjectName(name) {
    // Allow letters, numbers, spaces, hyphens, underscores
    const nameRegex = /^[a-zA-Z0-9\s\-_]{1,100}$/;
    return nameRegex.test(name);
  }

  // Rate limiting configurations
  createRateLimiter(windowMs = 15 * 60 * 1000, max = 100, message = 'Too many requests') {
    return rateLimit({
      windowMs,
      max,
      message: {
        error: 'Rate limit exceeded',
        message,
        retryAfter: Math.ceil(windowMs / 1000)
      },
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        logger.warn('Rate limit exceeded', {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.originalUrl
        });
        res.status(429).json({
          error: 'Rate limit exceeded',
          message,
          retryAfter: Math.ceil(windowMs / 1000)
        });
      }
    });
  }

  // Webhook signature verification
  verifyWebhookSignature(payload, signature, secret) {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(payload)
        .digest('hex');

      const providedSignature = signature.replace('sha256=', '');
      
      return crypto.timingSafeEqual(
        Buffer.from(expectedSignature),
        Buffer.from(providedSignature)
      );
    } catch (error) {
      logger.error('Webhook signature verification error:', error);
      return false;
    }
  }

  // Generate secure random strings
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  generateApiKey() {
    const prefix = 'wa_ai_';
    const randomPart = this.generateSecureToken(16);
    return prefix + randomPart;
  }

  // Content security
  isContentSafe(content) {
    // Check for potentially malicious content
    const dangerousPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /onload=/i,
      /onerror=/i,
      /onclick=/i,
      /eval\(/i,
      /document\.cookie/i,
      /window\.location/i
    ];

    return !dangerousPatterns.some(pattern => pattern.test(content));
  }

  sanitizeFileName(fileName) {
    return fileName
      .replace(/[^a-zA-Z0-9\-_.]/g, '_') // Replace invalid chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .substring(0, 255); // Limit length
  }

  // IP and request validation
  isValidIP(ip) {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  isPrivateIP(ip) {
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^127\./,
      /^169\.254\./,
      /^::1$/,
      /^fc00:/,
      /^fe80:/
    ];
    
    return privateRanges.some(range => range.test(ip));
  }

  // Session security
  generateSessionId() {
    return this.generateSecureToken(32);
  }

  isSessionExpired(sessionTimestamp, timeoutMinutes = 30) {
    const now = new Date();
    const sessionTime = new Date(sessionTimestamp);
    const diffMinutes = (now - sessionTime) / (1000 * 60);
    return diffMinutes > timeoutMinutes;
  }

  // API key validation
  validateApiKey(apiKey) {
    if (!apiKey || typeof apiKey !== 'string') {
      return false;
    }

    // Check format
    if (!apiKey.startsWith('wa_ai_')) {
      return false;
    }

    // Check length (prefix + 32 hex chars)
    if (apiKey.length !== 38) {
      return false;
    }

    // Check if the rest is valid hex
    const hexPart = apiKey.substring(6);
    return /^[a-f0-9]{32}$/.test(hexPart);
  }

  // Environment validation
  validateEnvironmentVariables() {
    const required = [
      'JWT_SECRET',
      'MONGODB_URI',
      'WHATSAPP_ACCESS_TOKEN',
      'WHATSAPP_PHONE_NUMBER_ID'
    ];

    const missing = required.filter(env => !process.env[env]);
    
    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }

    // Validate JWT secret strength
    if (process.env.JWT_SECRET.length < 32) {
      throw new Error('JWT_SECRET must be at least 32 characters long');
    }

    return true;
  }
}

module.exports = new SecurityUtils();
