const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();

// Basic health check
router.get('/', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Detailed health check with dependencies
router.get('/detailed', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version || '1.0.0',
    dependencies: {}
  };

  // Check MongoDB connection
  try {
    if (mongoose.connection.readyState === 1) {
      health.dependencies.mongodb = { status: 'connected' };
    } else {
      health.dependencies.mongodb = { status: 'disconnected' };
      health.status = 'unhealthy';
    }
  } catch (error) {
    health.dependencies.mongodb = { status: 'error', message: error.message };
    health.status = 'unhealthy';
  }

  // Check environment variables
  const requiredEnvVars = [
    'WHATSAPP_ACCESS_TOKEN',
    'WHATSAPP_PHONE_NUMBER_ID',
    'MONGODB_URI'
  ];

  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
  if (missingEnvVars.length > 0) {
    health.dependencies.environment = {
      status: 'error',
      missing: missingEnvVars
    };
    health.status = 'unhealthy';
  } else {
    health.dependencies.environment = { status: 'ok' };
  }

  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
});

module.exports = router;
