const express = require('express');
const crypto = require('crypto');
const WhatsAppHandler = require('../handlers/WhatsAppHandler');
const logger = require('../utils/logger');
const { AppError } = require('../middleware/errorHandler');

const router = express.Router();

// Webhook verification middleware
const verifyWebhookSignature = (req, res, next) => {
  if (process.env.NODE_ENV === 'development') {
    return next(); // Skip verification in development
  }

  const signature = req.headers['x-hub-signature-256'];
  const webhookSecret = process.env.WEBHOOK_SECRET;

  if (!signature || !webhookSecret) {
    return next(new AppError('Missing webhook signature or secret', 401));
  }

  const expectedSignature = crypto
    .createHmac('sha256', webhookSecret)
    .update(JSON.stringify(req.body))
    .digest('hex');

  const signatureHash = signature.replace('sha256=', '');

  if (!crypto.timingSafeEqual(Buffer.from(expectedSignature), Buffer.from(signatureHash))) {
    return next(new AppError('Invalid webhook signature', 401));
  }

  next();
};

// WhatsApp webhook verification (GET request)
router.get('/', (req, res) => {
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  // Verify the webhook
  if (mode === 'subscribe' && token === process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN) {
    logger.info('WhatsApp webhook verified successfully');
    res.status(200).send(challenge);
  } else {
    logger.warn('WhatsApp webhook verification failed', { mode, token });
    res.status(403).send('Forbidden');
  }
});

// WhatsApp webhook handler (POST request)
router.post('/', verifyWebhookSignature, async (req, res) => {
  try {
    const body = req.body;
    
    logger.info('Received WhatsApp webhook', { 
      body: JSON.stringify(body, null, 2) 
    });

    // Acknowledge receipt immediately
    res.status(200).send('OK');

    // Process the webhook asynchronously
    if (body.object === 'whatsapp_business_account') {
      const whatsappHandler = new WhatsAppHandler();
      await whatsappHandler.handleWebhook(body);
    } else {
      logger.warn('Received unknown webhook object type', { object: body.object });
    }

  } catch (error) {
    logger.error('Error processing WhatsApp webhook:', error);
    // Still return 200 to prevent WhatsApp from retrying
    res.status(200).send('OK');
  }
});

// Test endpoint for development
if (process.env.NODE_ENV === 'development') {
  router.post('/test', async (req, res) => {
    try {
      const { phoneNumber, message } = req.body;
      
      if (!phoneNumber || !message) {
        return res.status(400).json({
          error: 'Missing required fields: phoneNumber and message'
        });
      }

      const whatsappHandler = new WhatsAppHandler();
      const mockWebhookData = {
        object: 'whatsapp_business_account',
        entry: [{
          id: 'test-entry-id',
          changes: [{
            value: {
              messaging_product: 'whatsapp',
              metadata: {
                display_phone_number: process.env.WHATSAPP_PHONE_NUMBER_ID,
                phone_number_id: process.env.WHATSAPP_PHONE_NUMBER_ID
              },
              messages: [{
                from: phoneNumber,
                id: `test-msg-${Date.now()}`,
                timestamp: Math.floor(Date.now() / 1000).toString(),
                text: { body: message },
                type: 'text'
              }]
            },
            field: 'messages'
          }]
        }]
      };

      await whatsappHandler.handleWebhook(mockWebhookData);
      
      res.json({
        success: true,
        message: 'Test message processed successfully'
      });

    } catch (error) {
      logger.error('Error in test endpoint:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: error.message
      });
    }
  });
}

module.exports = router;
